document.addEventListener('DOMContentLoaded', function(){
  const form = document.getElementById('login-form');
  const toggle = document.getElementById('toggle-password');
  const pwd = document.getElementById('password');
  toggle.addEventListener('click', function(){
    const shown = pwd.type === 'text';
    pwd.type = shown ? 'password' : 'text';
    toggle.textContent = shown ? 'Show' : 'Hide';
    toggle.setAttribute('aria-pressed', String(!shown));
  });

  form.addEventListener('submit', function(ev){
    ev.preventDefault();
    // simple validation
    const id = document.getElementById('identifier');
    const errors = [];
    if(!id.value.trim()) errors.push('Please enter your email or username');
    if(!pwd.value) errors.push('Please enter your password');
    if(errors.length){
      alert(errors.join('\n'));
      return;
    }
    // fake submit
    const btn = form.querySelector('.submit');
    btn.disabled = true;
    btn.textContent = 'Signing in...';
    setTimeout(()=>{
      btn.disabled = false;
      btn.textContent = 'Sign in';
      alert('Login simulated. Replace with real authentication.');
    }, 900);
  });
});