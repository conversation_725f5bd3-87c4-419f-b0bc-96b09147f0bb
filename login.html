<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Login — Example</title>
  <link rel="stylesheet" href="css/style.css">
</head>
<body>
  <main class="container">
    <section class="login-card" aria-labelledby="login-heading">
      <h1 id="login-heading">Welcome back</h1>
      <p class="lead">Sign in to continue to your account</p>
      <form id="login-form" novalidate>
        <label for="identifier">Email or username</label>
        <input id="identifier" name="identifier" type="text" inputmode="email" autocomplete="username" required>

        <label for="password">Password</label>
        <div class="password-row">
          <input id="password" name="password" type="password" autocomplete="current-password" required minlength="6">
          <button type="button" id="toggle-password" aria-pressed="false" aria-label="Show password">Show</button>
        </div>

        <div class="row">
          <label class="checkbox-label"><input type="checkbox" id="remember" name="remember"> Remember me</label>
          <a class="forgot" href="#">Forgot?</a>
        </div>

        <button class="submit" type="submit">Sign in</button>

        <div class="divider"><span>or</span></div>

        <button type="button" class="social google">Continue with Google</button>
      </form>

      <p class="signup">Don't have an account? <a href="#">Sign up</a></p>
    </section>
  </main>

  <script src="js/login.js"></script>
</body>
</html>